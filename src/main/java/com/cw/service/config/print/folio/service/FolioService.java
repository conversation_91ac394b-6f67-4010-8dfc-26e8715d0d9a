package com.cw.service.config.print.folio.service;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.List;

import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import com.cw.entity.GuestAccounts;
import com.cw.entity.Reservation;
import com.cw.mapper.GuestAccountMapper;
import com.cw.mapper.ProfileMapper;
import com.cw.mapper.ReservationMapper;
import com.cw.mapper.common.DaoLocal;
import com.cw.report.export.ExportProviderFactory;
import com.cw.service.config.hotel.HotelService;
import com.cw.service.config.print.folio.FolioJasperGenerator;
import com.cw.service.context.GlobalContext;

import cn.hutool.core.util.CharsetUtil;
import jodd.io.FileUtil;

@Slf4j
@Service
public class FolioService {
	@Resource
	private DaoLocal<?> daoLocal;
	@Resource
	private ReservationMapper reservationMapper;
	@Resource
	private ProfileMapper profileMapper;
	@Resource
	private GuestAccountMapper guestAccountMapper;
	@Resource
	private HotelService hotelService;

	public void print2OutputStream(String resNo, com.cw.report.enums.ExportType exportType, OutputStream outputStream) throws Exception {
		String hotelid = GlobalContext.getCurrentHotelId();
		byte[] jrxmlBytes = loadJrxmlTemplate(hotelid);
		Reservation reservation = reservationMapper.findByHotelIdAndReservationNumber(hotelid, resNo);
		List<GuestAccounts> accList = daoLocal.getObjectList("from GuestAccounts where hotelId=?1 and reservationNumber=?2 and internal=?3 order by business_date", hotelid, resNo,
				false);
		ExportProviderFactory.createProvider(exportType).export2Stream(new FolioJasperGenerator()//
				.setHotelInfo(hotelService.findHotelInfo())//
				.setResv(reservation)//
				.setAccList(accList)//
				.generateReport(jrxmlBytes), outputStream);
	}

	public byte[] getBytesPrint(String resNo, com.cw.report.enums.ExportType exportType) throws Exception {
		try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
			print2OutputStream(resNo, exportType, outputStream);
			return outputStream.toByteArray();
		}
	}

	/**
	 * 加载JRXML模板文件
	 * 先尝试加载酒店特定的模板，如果不存在则加载默认模板
	 *
	 * @param hotelId 酒店ID
	 * @return 模板文件的字节数组
	 * @throws IOException 如果无法读取模板文件
	 */
	private byte[] loadJrxmlTemplate(String hotelId) throws IOException {
		// 先尝试加载酒店特定的模板
		String hotelSpecificPath = "templates/folio/" + hotelId + ".jrxml";
		InputStream hotelSpecificStream = getClass().getClassLoader().getResourceAsStream(hotelSpecificPath);

		if (hotelSpecificStream != null) {
			try {
				log.info("成功加载{}酒店模板路径信息",hotelSpecificPath);
				return readAllBytes(hotelSpecificStream);
			} finally {
				hotelSpecificStream.close();
			}
		}

		// 如果酒店特定模板不存在，加载默认模板
		String defaultPath = "templates/folio/default.jrxml";
		InputStream defaultStream = getClass().getClassLoader().getResourceAsStream(defaultPath);

		if (defaultStream == null) {
			throw new IOException("无法找到默认模板文件: " + defaultPath);
		}

		try {
			return readAllBytes(defaultStream);
		} finally {
			defaultStream.close();
		}
	}

	/**
	 * 读取输入流中的所有字节
	 *
	 * @param inputStream 输入流
	 * @return 字节数组
	 * @throws IOException 如果读取过程中发生错误
	 */
	private byte[] readAllBytes(InputStream inputStream) throws IOException {
		ByteArrayOutputStream buffer = new ByteArrayOutputStream();
		int nRead;
		byte[] data = new byte[4096];

		while ((nRead = inputStream.read(data, 0, data.length)) != -1) {
			buffer.write(data, 0, nRead);
		}

		return buffer.toByteArray();
	}
}
